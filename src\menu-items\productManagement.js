// assets
import {
  IconPackage,
  IconShoppingCart,
  IconTags,
  IconDiscount,
  IconCategory,
  IconListDetails,
  IconPlus,
  IconCalendarEvent,
  IconBrandApple
} from '@tabler/icons-react';

// constant
const icons = {
  IconPackage,
  IconShoppingCart,
  IconTags,
  IconDiscount,
  IconCategory,
  IconListDetails,
  IconPlus,
  IconCalendarEvent,
  IconBrandApple
};

// ==============================|| PRODUCT MANAGEMENT MENU ITEMS ||============================== //

const productManagement = {
  id: 'product-management',
  title: 'Gestion des Produits',
  type: 'group',
  children: [
    {
      id: 'products',
      title: 'Produits',
      type: 'collapse',
      icon: icons.IconPackage,
      children: [
        {
          id: 'products-list',
          title: 'Liste des Produits',
          type: 'item',
          url: '/app/typography',
          breadcrumbs: false
        },
        {
          id: 'products-add',
          title: 'Ajouter un Produit',
          type: 'item',
          url: '/app/AjoutProduit',
          breadcrumbs: false
        }
      ]
    },
    {
      id: 'categories',
      title: 'Catégories',
      type: 'collapse',
      icon: icons.IconCategory,
      children: [
        {
          id: 'categories-management',
          title: 'Gestion des Catégories',
          type: 'item',
          url: '/app/categories',
          breadcrumbs: false
        },
        {
          id: 'featured-categories',
          title: 'Catégories Mises en Avant',
          type: 'item',
          url: '/app/categories#featured',
          breadcrumbs: false
        }
      ]
    },
    {
      id: 'attributes',
      title: 'Attributs',
      type: 'item',
      url: '/app/attributes',
      icon: icons.IconListDetails,
      breadcrumbs: false
    },
    {
      id: 'brands',
      title: 'Marques',
      type: 'item',
      url: '/app/brands',
      icon: icons.IconBrandApple,
      breadcrumbs: false
    },
    {
      id: 'collections',
      title: 'Collections',
      type: 'collapse',
      icon: icons.IconTags,
      children: [
        {
          id: 'collections-list',
          title: 'Liste des Collections',
          type: 'item',
          url: '/app/LCollection',
          breadcrumbs: false
        },
        {
          id: 'collections-add',
          title: 'Ajouter une Collection',
          type: 'item',
          url: '/app/Collection',
          breadcrumbs: false
        }
      ]
    },
    {
      id: 'promotions',
      title: 'Promotions',
      type: 'collapse',
      icon: icons.IconDiscount,
      children: [
        {
          id: 'promotions-list',
          title: 'Liste des Promotions',
          type: 'item',
          url: '/app/Lpromotions',
          breadcrumbs: false
        },
        {
          id: 'promotions-add',
          title: 'Ajouter une Promotion',
          type: 'item',
          url: '/app/Promotion',
          breadcrumbs: false
        },
        {
          id: 'promotions-events',
          title: 'Événements Promotionnels',
          type: 'item',
          url: '/app/PromotionEvent',
          breadcrumbs: false
        }
      ]
    }
  ]
};

export default productManagement;
