import { lazy } from 'react';

// project imports
import MainLayout from 'layout/MainLayout';
import Loadable from 'ui-component/Loadable';
import ProtectedRoute from 'components/ProtectedRoute';
import LandingPage from '../views/pages/LandingPage';
import AjoutProduit from '../views/Forms/AjoutProduit';
import Collection from '../views/Forms/Collection';
import Promotion from '../views/Forms/Promotion';
import Listpromotions from '../views/GestionCommerciale/Listpromotions';
import ListCollection from '../views/GestionCommerciale/ListCollection';
import PromotionEvent from '../views/Forms/PromotionEvent';

import CategoriesManagement from '../views/GestionCommerciale/CategoriesManagement';
import SousSousCategories from '../views/GestionCommerciale/SousSousCategories';
import AttributeManagement from '../views/GestionCommerciale/AttributeManagement';
import BrandManagement from '../views/GestionCommerciale/BrandManagement';
import CarouselManagement from '../views/ContentManagement/CarouselManagement';
import Homepage from '../views/ContentManagement/Homepage';
import Pages from '../views/ContentManagement/Pages';
import Layouts from '../views/ContentManagement/Layouts';
import ClientGroupManagement from '../views/ClientManagement/ClientGroupManagement';
import ClientDiscountProfiles from '../views/ClientManagement/ClientDiscountProfiles';

import OrderDetail from '../views/OrderManagement/OrderDetail';
import OrderStatusManagement from '../views/OrderManagement/OrderStatusManagement';
import Invoices from '../views/OrderManagement/Invoices';

// Order Management routing
const OrderList = Loadable(lazy(() => import('views/OrderManagement/OrderList')));

// Client Management routing
const ClientList = Loadable(lazy(() => import('views/ClientManagement/ClientList')));

// dashboard routing
const DashboardDefault = Loadable(lazy(() => import('views/dashboard/Default')));

// utilities routing
const UtilsTypography = Loadable(lazy(() => import('views/utilities/Typography')));
const UtilsColor = Loadable(lazy(() => import('views/utilities/Color')));
const UtilsShadow = Loadable(lazy(() => import('views/utilities/Shadow')));

// sample page routing
const SamplePage = Loadable(lazy(() => import('views/sample-page')));

// profile page routing
const UserProfile = Loadable(lazy(() => import('views/profile/UserProfile')));

// debug page routing
const AuthDebug = Loadable(lazy(() => import('views/debug/AuthDebug')));
const OrdersDebug = Loadable(lazy(() => import('views/debug/OrdersDebug')));

// ==============================|| MAIN ROUTING ||============================== //

const MainRoutes = {
  path: '/app',
  element: (
    <ProtectedRoute>
      <MainLayout />
    </ProtectedRoute>
  ),
  children: [
    {
      path: '/app',
      element: <DashboardDefault />
    },
    {
      path: 'dashboard',
      children: [
        {
          path: 'default',
          element: <DashboardDefault />
        }
      ]
    },
    {
      path: 'typography',
      element: <UtilsTypography />
    },
    {
      path: 'color',
      element: <UtilsColor />
    },
    {
      path: 'shadow',
      element: <UtilsShadow />
    },
    {
      path: 'sample-page',
      element: <SamplePage />
    },
    {
      path: 'profile',
      element: <UserProfile />
    },
    {
      path: 'debug/auth',
      element: <AuthDebug />
    },
    {
      path: 'debug/orders',
      element: <OrdersDebug />
    },
    {
      path: 'AjoutProduit',
      element: <AjoutProduit />
    },
    {
      path: 'Collection',
      element: <Collection />
    },
    {
      path: 'Promotion',
      element: <Promotion />
    },

    {
      path: 'Lpromotions',
      element: <Listpromotions />
    },
    {
      path: 'LCollection',
      element: <ListCollection />
    },
    {
      path: 'PromotionEvent',
      element: <PromotionEvent />
    },

    {
      path: 'categories',
      element: <CategoriesManagement />
    },
    {
      path: 'sous-categories',
      element: <CategoriesManagement />
    },
    {
      path: 'sous-sous-categories',
      element: <SousSousCategories />
    },
    {
      path: 'attributes',
      element: <AttributeManagement />
    },
    {
      path: 'brands',
      element: <BrandManagement />
    },
    {
      path: 'carousels',
      element: <CarouselManagement />
    },
    {
      path: 'homepage',
      element: <Homepage />
    },
    {
      path: 'pages',
      element: <Pages />
    },
    {
      path: 'layouts',
      element: <Layouts />
    },
    {
      path: 'invoices',
      element: <Invoices />
    },
    {
      path: 'client-discount-profiles',
      element: <ClientDiscountProfiles />
    },
    {
      path: 'orders/:id',
      element: <OrderDetail />
    },
    {
      path: 'order-statuses',
      element: <OrderStatusManagement />
    },
    {
      path: 'orders',
      children: [
        {
          path: '',
          element: <OrderList />
        },
        {
          path: 'list',
          element: <OrderList />
        },
        {
          path: 'new',
          element: (
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <h3>Nouvelle Commande</h3>
              <p>Cette fonctionnalité sera bientôt disponible.</p>
            </div>
          )
        }
        // Add more order routes here, e.g., /orders/details/:orderId
      ]
    },
    {
      path: 'clients',
      children: [
        {
          path: 'list',
          element: <ClientList />
        },
        {
          path: 'groups',
          element: <ClientGroupManagement />
        }
      ]
    }
  ]
};

export default MainRoutes;
