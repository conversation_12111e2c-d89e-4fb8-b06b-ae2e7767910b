/* Professional Modal Styles */

.professional-modal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: #ffffff;
}

.professional-modal-dialog {
  margin: 1rem auto;
  margin-top: calc(88px + 1rem); /* Account for fixed navbar (88px) + standard margin */
  max-width: none;
}

/* Professional sizing that avoids sidebar/navbar overlap */
.professional-modal .modal-sm {
  max-width: 420px;
  width: 420px;
}

.professional-modal .modal-md {
  max-width: 600px;
  width: 600px;
}

.professional-modal .modal-lg {
  max-width: 800px;
  width: 800px;
}

.professional-modal .modal-xl {
  max-width: 1000px;
  width: 1000px;
}

/* Ensure proper centering with sidebar consideration */
@media (min-width: 576px) {
  .professional-modal .modal-dialog {
    margin: 1rem auto;
    margin-top: calc(88px + 1rem); /* Account for fixed navbar */
  }
}

/* Desktop layout - account for sidebar */
@media (min-width: 992px) {
  .professional-modal .modal-dialog {
    margin: 1rem auto;
    margin-top: calc(88px + 1rem); /* Account for fixed navbar */
    /* Ensure modal doesn't overlap with sidebar (260px) */
    margin-left: max(1rem, calc((100vw - 260px - var(--modal-width, 600px)) / 2 + 260px));
    margin-right: max(1rem, calc((100vw - 260px - var(--modal-width, 600px)) / 2));
  }

  .professional-modal .modal-sm {
    --modal-width: 420px;
  }

  .professional-modal .modal-md {
    --modal-width: 600px;
  }

  .professional-modal .modal-lg {
    --modal-width: 800px;
  }

  .professional-modal .modal-xl {
    --modal-width: 1000px;
  }

  /* Fallback for browsers that don't support CSS custom properties */
  .professional-modal .modal-sm .modal-dialog {
    margin-top: calc(88px + 1rem);
    margin-left: max(1rem, calc((100vw - 260px - 420px) / 2 + 260px));
    margin-right: max(1rem, calc((100vw - 260px - 420px) / 2));
  }

  .professional-modal .modal-md .modal-dialog {
    margin-top: calc(88px + 1rem);
    margin-left: max(1rem, calc((100vw - 260px - 600px) / 2 + 260px));
    margin-right: max(1rem, calc((100vw - 260px - 600px) / 2));
  }

  .professional-modal .modal-lg .modal-dialog {
    margin-top: calc(88px + 1rem);
    margin-left: max(1rem, calc((100vw - 260px - 800px) / 2 + 260px));
    margin-right: max(1rem, calc((100vw - 260px - 800px) / 2));
  }

  .professional-modal .modal-xl .modal-dialog {
    margin-top: calc(88px + 1rem);
    margin-left: max(1rem, calc((100vw - 260px - 1000px) / 2 + 260px));
    margin-right: max(1rem, calc((100vw - 260px - 1000px) / 2));
  }
}

/* Professional Header Styling - Compact Design */
.professional-modal-header {
  padding: 0.75rem 1.25rem;
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  position: relative;
  min-height: auto;
}

.professional-modal-header .modal-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
}

.professional-modal-header .modal-title-main {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0;
  line-height: 1.3;
}

.professional-modal-header .modal-subtitle {
  font-size: 0.8rem;
  font-weight: 400;
  opacity: 0.9;
  margin-top: 0.2rem;
  line-height: 1.2;
}

.professional-modal-header .btn-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  opacity: 0.8;
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
}

.professional-modal-header .btn-close:hover {
  opacity: 1;
}

/* Header variants */
.professional-modal-header.bg-primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.professional-modal-header.bg-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.professional-modal-header.bg-success {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.professional-modal-header.bg-warning {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.professional-modal-header.bg-info {
  background: linear-gradient(135deg, #2196f3 0%, #0288d1 100%);
}

.professional-modal-header.bg-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* Professional Body Styling */
.professional-modal-body {
  padding: 1.5rem;
  background: #ffffff;
  min-height: 200px;
}

.professional-modal-body::-webkit-scrollbar {
  width: 6px;
}

.professional-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.professional-modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.professional-modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Professional Footer Styling - Compact Design */
.professional-modal-footer {
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
  display: flex;
  justify-content: flex-end;
  gap: 0.6rem;
  min-height: auto;
}

.professional-modal-footer .btn {
  min-width: 90px;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.4rem 1rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.professional-modal-footer .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.professional-modal-footer .btn:active {
  transform: translateY(0);
}

.professional-modal-footer .btn-primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border: none;
}

.professional-modal-footer .btn-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  border: none;
}

.professional-modal-footer .btn-success {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  border: none;
}

.professional-modal-footer .btn-secondary {
  background: #6c757d;
  border: 1px solid #6c757d;
}

/* Loading states */
.professional-modal-footer .btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.professional-modal-footer .btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Animation for modal appearance */
.professional-modal .modal-dialog {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Backdrop styling */
.professional-modal .modal-backdrop {
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
}

/* Ensure modal is positioned correctly relative to fixed navbar */
.professional-modal {
  z-index: 1200; /* Higher than Material-UI AppBar (1100) and Drawer (1200) */
}

.professional-modal .modal-backdrop {
  z-index: 1199; /* Just below modal */
}

/* Override Bootstrap modal z-index for professional modals */
.professional-modal.modal {
  z-index: 1200 !important;
}

.professional-modal .modal-dialog {
  z-index: 1201; /* Ensure dialog is above backdrop */
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
  /* Reset modal positioning for mobile/tablet */
  .professional-modal .modal-dialog {
    margin: 1rem auto !important;
    margin-top: calc(88px + 1rem) !important; /* Account for fixed navbar */
    margin-left: auto !important;
    margin-right: auto !important;
  }

  .professional-modal .modal-sm {
    max-width: 90vw;
    width: 90vw;
  }

  .professional-modal .modal-md {
    max-width: 90vw;
    width: 90vw;
  }

  .professional-modal .modal-lg {
    max-width: 95vw;
    width: 95vw;
  }

  .professional-modal .modal-xl {
    max-width: 98vw;
    width: 98vw;
  }

  .professional-modal-header {
    padding: 0.6rem 1rem;
  }

  .professional-modal-body {
    padding: 1rem;
  }

  .professional-modal-footer {
    padding: 0.6rem 1rem;
  }
}

@media (max-width: 575.98px) {
  .professional-modal .modal-dialog {
    margin: 0.5rem;
    margin-top: calc(88px + 0.5rem) !important; /* Account for fixed navbar on mobile */
  }

  .professional-modal .modal-sm,
  .professional-modal .modal-md,
  .professional-modal .modal-lg,
  .professional-modal .modal-xl {
    max-width: calc(100vw - 1rem);
    width: calc(100vw - 1rem);
  }

  .professional-modal-header {
    padding: 0.5rem 0.75rem;
  }

  .professional-modal-header .modal-title-main {
    font-size: 0.9rem;
  }

  .professional-modal-body {
    padding: 0.75rem;
  }

  .professional-modal-footer {
    flex-direction: column;
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
  }

  .professional-modal-footer .btn {
    width: 100%;
    margin-bottom: 0;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Focus states for accessibility */
.professional-modal-header .btn-close:focus,
.professional-modal-footer .btn:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .professional-modal {
    display: none !important;
  }
}
