import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import PropTypes from 'prop-types';
import './ProfessionalModal.css';

// Professional Modal Header Component
export const ModalHeader = ({ title, subtitle, icon, variant = 'primary', onClose, showCloseButton = true }) => {
  const getVariantClass = () => {
    switch (variant) {
      case 'danger':
        return 'bg-danger text-white';
      case 'warning':
        return 'bg-warning text-dark';
      case 'success':
        return 'bg-success text-white';
      case 'info':
        return 'bg-info text-white';
      case 'secondary':
        return 'bg-secondary text-white';
      default:
        return 'bg-primary text-white';
    }
  };

  return (
    <Modal.Header closeButton={showCloseButton} className={`professional-modal-header ${getVariantClass()}`} onHide={onClose}>
      <Modal.Title className="d-flex align-items-center w-100">
        {icon && <span className="me-2">{icon}</span>}
        <div className="flex-grow-1">
          <div className="modal-title-main">{title}</div>
          {subtitle && <div className="modal-subtitle">{subtitle}</div>}
        </div>
      </Modal.Title>
    </Modal.Header>
  );
};

// Professional Modal Footer Component
export const ModalFooter = ({
  children,
  primaryButton,
  secondaryButton,
  loading = false,
  primaryAction,
  secondaryAction,
  primaryVariant = 'primary',
  secondaryVariant = 'secondary',
  primaryText = 'Confirmer',
  secondaryText = 'Annuler',
  loadingText = 'Traitement...',
  disabled = false
}) => {
  return (
    <Modal.Footer className="professional-modal-footer">
      {children ? (
        children
      ) : (
        <>
          {secondaryButton !== false && (
            <Button variant={secondaryVariant} onClick={secondaryAction} disabled={loading || disabled} className="me-2">
              {secondaryText}
            </Button>
          )}
          {primaryButton !== false && (
            <Button variant={primaryVariant} onClick={primaryAction} disabled={loading || disabled}>
              {loading ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  {loadingText}
                </>
              ) : (
                primaryText
              )}
            </Button>
          )}
        </>
      )}
    </Modal.Footer>
  );
};

// Main Professional Modal Component
const ProfessionalModal = ({
  show,
  onHide,
  title,
  subtitle,
  icon,
  children,
  size = 'md',
  centered = true,
  backdrop = 'static',
  keyboard = false,
  variant = 'primary',

  // Header props
  showHeader = true,
  showCloseButton = true,

  // Footer props
  showFooter = true,
  primaryButton = true,
  secondaryButton = true,
  primaryAction,
  secondaryAction,
  primaryVariant = 'primary',
  secondaryVariant = 'secondary',
  primaryText = 'Confirmer',
  secondaryText = 'Annuler',
  loading = false,
  loadingText = 'Traitement...',
  disabled = false,

  // Body props
  bodyClassName = '',
  bodyStyle = {},
  maxHeight = '70vh',

  // Modal props
  className = '',
  dialogClassName = '',

  // Custom components
  customHeader,
  customFooter,

  ...otherProps
}) => {
  const modalClassName = `professional-modal ${className}`;
  const dialogClass = `professional-modal-dialog ${dialogClassName}`;

  const defaultBodyStyle = {
    maxHeight,
    overflowY: 'auto',
    ...bodyStyle
  };

  const handleSecondaryAction = () => {
    if (secondaryAction) {
      secondaryAction();
    } else {
      onHide();
    }
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      size={size}
      centered={centered}
      backdrop={backdrop}
      keyboard={keyboard}
      className={modalClassName}
      dialogClassName={dialogClass}
      {...otherProps}
    >
      {showHeader &&
        (customHeader || (
          <ModalHeader title={title} subtitle={subtitle} icon={icon} variant={variant} onClose={onHide} showCloseButton={showCloseButton} />
        ))}

      <Modal.Body className={`professional-modal-body ${bodyClassName}`} style={defaultBodyStyle}>
        {children}
      </Modal.Body>

      {showFooter &&
        (customFooter || (
          <ModalFooter
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
            primaryAction={primaryAction}
            secondaryAction={handleSecondaryAction}
            primaryVariant={primaryVariant}
            secondaryVariant={secondaryVariant}
            primaryText={primaryText}
            secondaryText={secondaryText}
            loading={loading}
            loadingText={loadingText}
            disabled={disabled}
          />
        ))}
    </Modal>
  );
};

// PropTypes
ModalHeader.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string,
  icon: PropTypes.node,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger', 'warning', 'info']),
  onClose: PropTypes.func,
  showCloseButton: PropTypes.bool
};

ModalFooter.propTypes = {
  children: PropTypes.node,
  primaryButton: PropTypes.bool,
  secondaryButton: PropTypes.bool,
  loading: PropTypes.bool,
  primaryAction: PropTypes.func,
  secondaryAction: PropTypes.func,
  primaryVariant: PropTypes.string,
  secondaryVariant: PropTypes.string,
  primaryText: PropTypes.string,
  secondaryText: PropTypes.string,
  loadingText: PropTypes.string,
  disabled: PropTypes.bool
};

ProfessionalModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  icon: PropTypes.node,
  children: PropTypes.node.isRequired,
  size: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  centered: PropTypes.bool,
  backdrop: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
  keyboard: PropTypes.bool,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger', 'warning', 'info']),
  showHeader: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  showFooter: PropTypes.bool,
  primaryButton: PropTypes.bool,
  secondaryButton: PropTypes.bool,
  primaryAction: PropTypes.func,
  secondaryAction: PropTypes.func,
  primaryVariant: PropTypes.string,
  secondaryVariant: PropTypes.string,
  primaryText: PropTypes.string,
  secondaryText: PropTypes.string,
  loading: PropTypes.bool,
  loadingText: PropTypes.string,
  disabled: PropTypes.bool,
  bodyClassName: PropTypes.string,
  bodyStyle: PropTypes.object,
  maxHeight: PropTypes.string,
  className: PropTypes.string,
  dialogClassName: PropTypes.string,
  customHeader: PropTypes.node,
  customFooter: PropTypes.node
};

export default ProfessionalModal;
