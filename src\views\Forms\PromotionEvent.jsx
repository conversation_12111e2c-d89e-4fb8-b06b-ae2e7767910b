import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, Card, Form, Button, Row, Col, Al<PERSON>, Badge, Modal, Table, Image, Spinner } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import axios from 'axios';

const API_URL = 'https://laravel-api.fly.dev/api';

const PromotionEvent = () => {
  // Styles
  const colors = {
    primary: '#1a237e',
    secondary: '#283593',
    accent: '#3949ab',
    light: '#e8eaf6',
    dark: '#0d47a1',
    success: '#28a745',
    info: '#17a2b8',
    warning: '#ffc107',
    danger: '#dc3545'
  };

  const fontStyle = {
    fontFamily: "'Montserrat', sans-serif",
    fontWeight: 500
  };

  // État initial
  const initialFormData = {
    // Événement
    nom: 'Promotion Spéciale',
    code: '',
    description: '',
    couleur: '#1a237e',
    icone: 'tag',
    actif: true,
    date_debut: null,
    date_fin: null,
    // Promotion
    type: 'pourcentage',
    valeur: 15,
    statut: 'active',
    cumulable: false,
    conditions: 'Valable sur les produits sélectionnés',
    profils_remise: ['standard'],
    produits: [],
    image: 'https://via.placeholder.com/800x400?text=Promotion',
    priorite: 10,
    featured: false
  };

  // États
  const [formData, setFormData] = useState(initialFormData);
  const [products, setProducts] = useState([]);
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Options
  const clientProfileOptions = [
    { value: 'standard', label: 'Standard' },
    { value: 'premium', label: 'Premium' },
    { value: 'affilie', label: 'Affilié' },
    { value: 'groupe', label: 'Groupe' }
  ];

  // Fonctions utilitaires
  const formatDateForAPI = (date) => {
    if (!date) return null;
    return date.toISOString().split('T')[0]; // Format YYYY-MM-DD
  };

  const generateValidCode = (name) => {
    if (!name) return '';
    return name
      .toUpperCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Supprime les accents
      .replace(/\s+/g, '_')
      .replace(/[^A-Z0-9_]/g, '')
      .substring(0, 50);
  };

  const handleApiError = (err) => {
    console.error("Détails de l'erreur:", err.response?.data);

    let errorMessage = 'Erreur lors de la création';
    const apiError = err.response?.data;

    if (apiError?.errors) {
      errorMessage = Object.entries(apiError.errors)
        .map(([field, messages]) => {
          // Traduction des noms de champs pour l'utilisateur
          const fieldNames = {
            nom: 'Nom',
            code: 'Code',
            date_debut: 'Date de début',
            date_fin: 'Date de fin',
            valeur: 'Valeur',
            type: 'Type de réduction',
            statut: 'Statut',
            profils_remise: 'Profils autorisés'
          };

          return `• ${fieldNames[field] || field}: ${messages.join(', ')}`;
        })
        .join('\n');
    } else if (apiError?.message) {
      errorMessage = apiError.message;
    }

    setError(errorMessage);
  };

  const validateForm = () => {
    const errors = [];

    // Validation des champs requis
    if (!formData.nom?.trim()) errors.push('Le nom est obligatoire');
    if (!formData.date_debut) errors.push('La date de début est obligatoire');
    if (!formData.date_fin) errors.push('La date de fin est obligatoire');

    // Validation des dates
    if (formData.date_debut && formData.date_fin && formData.date_fin < formData.date_debut) {
      errors.push('La date de fin doit être après la date de début');
    }

    // Validation des valeurs numériques
    if (isNaN(formData.valeur)) {
      errors.push('La valeur doit être un nombre');
    } else {
      if (formData.valeur <= 0) errors.push('La valeur doit être positive');
      if (formData.type === 'pourcentage' && formData.valeur > 100) {
        errors.push('Le pourcentage ne peut pas dépasser 100%');
      }
    }

    // Validation des profils
    if (formData.profils_remise.length === 0) {
      errors.push('Au moins un profil doit être sélectionné');
    }

    return errors.length > 0 ? errors.join('\n') : null;
  };

  // Chargement des données initiales
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        const [productsRes, collectionsRes] = await Promise.all([
          axios.get(`${API_URL}/produits`),
          axios.get(`${API_URL}/collections?with_produits=true`)
        ]);

        setProducts(productsRes.data.data || []);
        setCollections(collectionsRes.data.data || []);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Erreur de chargement des données. Veuillez rafraîchir la page.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Gestion des changements de formulaire
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox' && name === 'profils_remise') {
      const updatedProfiles = formData.profils_remise.includes(value)
        ? formData.profils_remise.filter((profile) => profile !== value)
        : [...formData.profils_remise, value];

      setFormData((prev) => ({
        ...prev,
        profils_remise: updatedProfiles
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleDateChange = (date, field) => {
    setFormData((prev) => ({
      ...prev,
      [field]: date
    }));
  };

  // Gestion des produits
  const addFeaturedProduct = (product) => {
    if (!formData.produits.some((p) => p.id === product.id)) {
      setFormData((prev) => ({
        ...prev,
        produits: [...prev.produits, { id: product.id }]
      }));
    }
    setShowProductModal(false);
  };

  const addFeaturedCollection = async (collection) => {
    try {
      const response = await axios.get(`${API_URL}/collections/${collection.id}/produits`);
      const collectionProducts = response.data.data || [];

      const newProducts = collectionProducts.filter((p) => !formData.produits.some((fp) => fp.id === p.id)).map((p) => ({ id: p.id }));

      if (newProducts.length > 0) {
        setFormData((prev) => ({
          ...prev,
          produits: [...prev.produits, ...newProducts]
        }));
      }
      setShowCollectionModal(false);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Erreur lors de la récupération des produits de la collection');
    }
  };

  const removeFeaturedProduct = (productId) => {
    setFormData((prev) => ({
      ...prev,
      produits: prev.produits.filter((p) => p.id !== productId)
    }));
  };

  // Soumission du formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation côté client
    const validationErrors = validateForm();
    if (validationErrors) {
      setError(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // 1. Création de l'événement promotionnel
      const eventCode = generateValidCode(formData.nom);

      const eventPayload = {
        nom: formData.nom.trim(),
        code: eventCode,
        description: formData.description.trim(),
        couleur: formData.couleur,
        icone: formData.icone,
        actif: formData.actif,
        date_debut: formatDateForAPI(formData.date_debut),
        date_fin: formatDateForAPI(formData.date_fin)
      };

      const eventResponse = await axios.post(`${API_URL}/promotion-events`, eventPayload, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json'
        }
      });

      // 2. Création de la promotion associée
      const promotionPayload = {
        nom: `${formData.nom.trim()} - Promotion`,
        code: `${eventCode}_PROMO`,
        description: formData.description.trim(),
        type: formData.type,
        valeur: parseFloat(formData.valeur),
        statut: formData.statut,
        date_debut: formatDateForAPI(formData.date_debut),
        date_fin: formatDateForAPI(formData.date_fin),
        cumulable: formData.cumulable,
        conditions: formData.conditions,
        event_id: eventResponse.data.data.id,
        produits_ids: formData.produits.map((p) => p.id),
        profils_remise: formData.profils_remise,
        image: formData.image,
        priorite: formData.priorite,
        featured: formData.featured
      };

      await axios.post(`${API_URL}/promotions`, promotionPayload, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json'
        }
      });

      setSuccessMessage('Promotion créée avec succès!');
      setShowPreview(true);
      setFormData(initialFormData);
    } catch (err) {
      handleApiError(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calcul du prix après réduction
  const calculateDiscountedPrice = (price) => {
    const numericPrice = parseFloat(price) || 0;
    const discountValue = parseFloat(formData.valeur) || 0;

    if (formData.type === 'pourcentage') {
      return (numericPrice * (1 - discountValue / 100)).toFixed(2);
    } else {
      return Math.max(0, numericPrice - discountValue).toFixed(2);
    }
  };

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" style={{ color: colors.primary }} />
        <p className="mt-3">Chargement en cours...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4" style={fontStyle}>
      {/* En-tête */}
      <div className="text-center mb-5">
        <h1 style={{ color: colors.primary }}>CRÉATION DE PROMOTION</h1>
        <div
          style={{
            height: '3px',
            width: '120px',
            background: `linear-gradient(90deg, rgba(26, 35, 126, 0.2) 0%, ${colors.primary} 50%, rgba(26, 35, 126, 0.2) 100%)`,
            borderRadius: '3px',
            margin: '0 auto'
          }}
        ></div>
      </div>

      {/* Messages d'erreur/succès */}
      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          <div style={{ whiteSpace: 'pre-wrap' }}>
            <i className="fas fa-exclamation-triangle me-2"></i>
            {error}
          </div>
        </Alert>
      )}

      {successMessage && (
        <Alert variant="success" onClose={() => setSuccessMessage(null)} dismissible>
          <i className="fas fa-check-circle me-2"></i>
          {successMessage}
        </Alert>
      )}

      {/* Formulaire principal */}
      <Card className="shadow-sm mb-4">
        <Card.Body>
          <Form onSubmit={handleSubmit}>
            <Row>
              {/* Section Événement */}
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Nom de l'événement *</Form.Label>
                  <Form.Control type="text" name="nom" value={formData.nom} onChange={handleChange} required />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Code *</Form.Label>
                  <Form.Control type="text" name="code" value={generateValidCode(formData.nom)} readOnly />
                  <Form.Text className="text-muted">Généré automatiquement à partir du nom</Form.Text>
                </Form.Group>
              </Col>

              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control as="textarea" name="description" value={formData.description} onChange={handleChange} rows={3} />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de début *</Form.Label>
                  <DatePicker
                    selected={formData.date_debut}
                    onChange={(date) => handleDateChange(date, 'date_debut')}
                    selectsStart
                    startDate={formData.date_debut}
                    endDate={formData.date_fin}
                    className="form-control"
                    dateFormat="dd/MM/yyyy"
                    minDate={new Date()}
                    required
                  />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de fin *</Form.Label>
                  <DatePicker
                    selected={formData.date_fin}
                    onChange={(date) => handleDateChange(date, 'date_fin')}
                    selectsEnd
                    startDate={formData.date_debut}
                    endDate={formData.date_fin}
                    minDate={formData.date_debut || new Date()}
                    className="form-control"
                    dateFormat="dd/MM/yyyy"
                    required
                  />
                </Form.Group>
              </Col>

              {/* Section Promotion */}
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Type de réduction *</Form.Label>
                  <Form.Select name="type" value={formData.type} onChange={handleChange} required>
                    <option value="pourcentage">Pourcentage</option>
                    <option value="montant_fixe">Montant fixe</option>
                  </Form.Select>
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Valeur *</Form.Label>
                  <div className="input-group">
                    <Form.Control
                      type="number"
                      name="valeur"
                      value={formData.valeur}
                      onChange={handleChange}
                      min="0"
                      max={formData.type === 'pourcentage' ? '100' : undefined}
                      step="0.01"
                      required
                    />
                    <span className="input-group-text">{formData.type === 'pourcentage' ? '%' : '€'}</span>
                  </div>
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Statut *</Form.Label>
                  <Form.Select name="statut" value={formData.statut} onChange={handleChange} required>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="programmée">Programmée</option>
                  </Form.Select>
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Priorité</Form.Label>
                  <Form.Control type="number" name="priorite" value={formData.priorite} onChange={handleChange} min="1" />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Image promotionnelle</Form.Label>
                  <Form.Control type="text" name="image" value={formData.image} onChange={handleChange} placeholder="URL de l'image" />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="switch"
                    id="featured-switch"
                    label="Mise en avant"
                    checked={formData.featured}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        featured: e.target.checked
                      }))
                    }
                  />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Profils autorisés *</Form.Label>
                  <div>
                    {clientProfileOptions.map((profile) => (
                      <Form.Check
                        key={profile.value}
                        type="checkbox"
                        id={`profile-${profile.value}`}
                        label={profile.label}
                        name="profils_remise"
                        value={profile.value}
                        checked={formData.profils_remise.includes(profile.value)}
                        onChange={handleChange}
                        className="mb-2"
                      />
                    ))}
                  </div>
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="switch"
                    id="cumulable-switch"
                    label="Promotion cumulable"
                    checked={formData.cumulable}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        cumulable: e.target.checked
                      }))
                    }
                  />
                </Form.Group>
              </Col>

              {/* Produits */}
              <Col md={12}>
                <Form.Group className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <Form.Label>Produits en promotion</Form.Label>
                    <div>
                      <Button variant="outline-primary" size="sm" onClick={() => setShowProductModal(true)} className="me-2">
                        <i className="fas fa-plus me-2"></i>
                        Ajouter un produit
                      </Button>
                      <Button variant="outline-secondary" size="sm" onClick={() => setShowCollectionModal(true)}>
                        <i className="fas fa-layer-group me-2"></i>
                        Ajouter une collection
                      </Button>
                    </div>
                  </div>

                  {formData.produits.length > 0 ? (
                    <Table striped bordered hover>
                      <thead>
                        <tr>
                          <th>Produit</th>
                          <th>Prix original</th>
                          <th>Prix promo</th>
                          <th>Réduction</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.produits.map((product) => {
                          const productData = products.find((p) => p.id === product.id) || {};
                          const prix = parseFloat(productData.prix_produit) || 0;
                          const prixPromo = calculateDiscountedPrice(prix);

                          return (
                            <tr key={product.id}>
                              <td>{productData.nom_produit || 'Produit inconnu'}</td>
                              <td>{prix.toFixed(2)} €</td>
                              <td>{prixPromo} €</td>
                              <td>
                                <Badge bg="success">
                                  {formData.type === 'pourcentage' ? `-${formData.valeur}%` : `-${formData.valeur}€`}
                                </Badge>
                              </td>
                              <td>
                                <Button variant="outline-danger" size="sm" onClick={() => removeFeaturedProduct(product.id)}>
                                  <i className="fas fa-trash"></i>
                                </Button>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </Table>
                  ) : (
                    <Alert variant="info">Aucun produit sélectionné</Alert>
                  )}
                </Form.Group>
              </Col>

              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Conditions</Form.Label>
                  <Form.Control as="textarea" name="conditions" value={formData.conditions} onChange={handleChange} rows={2} />
                </Form.Group>
              </Col>

              <Col md={12} className="text-center mt-4">
                <Button variant="primary" type="submit" disabled={isSubmitting} style={{ minWidth: '200px' }}>
                  {isSubmitting ? (
                    <>
                      <Spinner as="span" size="sm" animation="border" className="me-2" />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-save me-2"></i>
                      Enregistrer la promotion
                    </>
                  )}
                </Button>
              </Col>
            </Row>
          </Form>
        </Card.Body>
      </Card>

      {/* Modal Produits */}
      <Modal show={showProductModal} onHide={() => setShowProductModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Sélectionner des produits</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Table striped bordered hover>
            <thead>
              <tr>
                <th>Produit</th>
                <th>Prix</th>
                <th>Catégorie</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {products
                .filter((p) => !formData.produits.some((fp) => fp.id === p.id))
                .map((product) => (
                  <tr key={product.id}>
                    <td>{product.nom_produit}</td>
                    <td>{(parseFloat(product.prix_produit) || 0).toFixed(2)} €</td>
                    <td>{product.sous_sous_categorie_id}</td>
                    <td>
                      <Button variant="outline-primary" size="sm" onClick={() => addFeaturedProduct(product)}>
                        <i className="fas fa-plus"></i>
                      </Button>
                    </td>
                  </tr>
                ))}
            </tbody>
          </Table>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowProductModal(false)}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal Collections */}
      <Modal
        show={showCollectionModal}
        onHide={() => setShowCollectionModal(false)}
        size="md"
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-secondary text-white">
          <Modal.Title>Sélectionner une collection</Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          <Table striped bordered hover>
            <thead>
              <tr>
                <th>Collection</th>
                <th>Nombre de produits</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {collections.map((collection) => (
                <tr key={collection.id}>
                  <td>{collection.nom}</td>
                  <td>{collection.produits?.length || 0}</td>
                  <td>
                    <Button variant="outline-primary" size="sm" onClick={() => addFeaturedCollection(collection)}>
                      <i className="fas fa-plus"></i>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Modal.Body>
        <Modal.Footer className="professional-modal-footer">
          <Button variant="secondary" onClick={() => setShowCollectionModal(false)}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal Aperçu */}
      <Modal
        show={showPreview}
        onHide={() => setShowPreview(false)}
        size="lg"
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-success text-white">
          <Modal.Title>Aperçu de la promotion</Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          <div className="text-center mb-4">
            <Image src={formData.image} fluid style={{ maxHeight: '300px', objectFit: 'contain' }} className="mb-3" />
            <h3 style={{ color: colors.primary }}>{formData.nom}</h3>
            <p className="text-muted">{formData.description}</p>
          </div>

          <Row className="mb-4">
            <Col md={6}>
              <div className="p-3 bg-light rounded">
                <h5>Dates</h5>
                <p>
                  {formData.date_debut?.toLocaleDateString()} - {formData.date_fin?.toLocaleDateString()}
                </p>
              </div>
            </Col>
            <Col md={6}>
              <div className="p-3 bg-light rounded">
                <h5>Réduction</h5>
                <h3 style={{ color: colors.primary }}>
                  {formData.type === 'pourcentage' ? `${formData.valeur}% de réduction` : `${formData.valeur}€ de réduction`}
                </h3>
              </div>
            </Col>
          </Row>

          <div className="mb-4">
            <h5>Détails</h5>
            <div className="d-flex flex-wrap gap-3">
              <Badge bg={formData.statut === 'active' ? 'success' : 'secondary'}>Statut: {formData.statut}</Badge>
              <Badge bg={formData.cumulable ? 'info' : 'warning'}>{formData.cumulable ? 'Cumulable' : 'Non cumulable'}</Badge>
              <Badge bg={formData.featured ? 'primary' : 'secondary'}>{formData.featured ? 'Mise en avant' : 'Standard'}</Badge>
              <Badge bg="dark">Priorité: {formData.priorite}</Badge>
            </div>
          </div>

          <div className="mb-4">
            <h5>Profils autorisés</h5>
            <div className="d-flex flex-wrap gap-2">
              {formData.profils_remise.map((profile) => (
                <Badge key={profile} bg="info">
                  {clientProfileOptions.find((p) => p.value === profile)?.label || profile}
                </Badge>
              ))}
            </div>
          </div>

          {formData.produits.length > 0 && (
            <div className="mb-4">
              <h5>Produits concernés ({formData.produits.length})</h5>
              <Table striped bordered hover>
                <thead>
                  <tr>
                    <th>Produit</th>
                    <th>Prix original</th>
                    <th>Prix promo</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.produits.slice(0, 5).map((product) => {
                    const productData = products.find((p) => p.id === product.id) || {};
                    const prix = parseFloat(productData.prix_produit) || 0;
                    const prixPromo = calculateDiscountedPrice(prix);

                    return (
                      <tr key={product.id}>
                        <td>{productData.nom_produit || 'Produit inconnu'}</td>
                        <td>{prix.toFixed(2)} €</td>
                        <td>{prixPromo} €</td>
                      </tr>
                    );
                  })}
                </tbody>
              </Table>
              {formData.produits.length > 5 && <p className="text-muted">+ {formData.produits.length - 5} autres produits</p>}
            </div>
          )}

          {formData.conditions && (
            <div>
              <h5>Conditions</h5>
              <p className="text-muted">{formData.conditions}</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="professional-modal-footer">
          <Button variant="secondary" onClick={() => setShowPreview(false)}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default PromotionEvent;
