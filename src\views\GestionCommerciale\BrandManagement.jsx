import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>er, <PERSON>, <PERSON><PERSON>, Modal, Form, Alert, Spinner, Badge, Row, Col, Card, Breadcrumb } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import { FaEdit, FaTrash, FaPlus, FaCheck, FaHome, FaTags, FaImage } from 'react-icons/fa';
import { fetchBrands, createBrand, updateBrand, deleteBrand } from '../../services/brandService';
import ImageManager from './ImageManager';

const BrandManagement = () => {
  // Helper function to convert actif field to boolean
  const convertActifToBoolean = (actif) => {
    // Handle different possible formats from API
    if (actif === null || actif === undefined) return false;
    if (typeof actif === 'boolean') return actif;
    if (typeof actif === 'string') {
      const lowerActif = actif.toLowerCase().trim();
      return lowerActif === '1' || lowerActif === 'true' || lowerActif === 'active' || lowerActif === 'yes';
    }
    if (typeof actif === 'number') return actif === 1;
    // Handle objects that might have a value property
    if (typeof actif === 'object' && actif.hasOwnProperty('value')) {
      return convertActifToBoolean(actif.value);
    }
    return false; // Default to false for any other case
  };

  // États
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [currentBrand, setCurrentBrand] = useState(null);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [formData, setFormData] = useState({
    nom_marque: '',
    description_marque: '',
    logo_marque: '',
    site_web: '',
    actif: true
  });

  // Test function to make direct API call
  const testDirectApiCall = async () => {
    try {
      const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';
      const response = await fetch(`${API_URL}/marques`);
      const rawData = await response.json();
      console.log('🧪 Direct API call result:', rawData);
      console.log('🧪 Response structure:', {
        hasData: !!rawData.data,
        isArray: Array.isArray(rawData),
        isDataArray: Array.isArray(rawData.data),
        keys: Object.keys(rawData),
        firstItem: rawData.data ? rawData.data[0] : rawData[0]
      });
    } catch (error) {
      console.error('🧪 Direct API call failed:', error);
    }
  };

  // Charger les marques
  useEffect(() => {
    const loadBrands = async () => {
      try {
        setLoading(true);

        // Make direct API call for debugging
        await testDirectApiCall();

        const data = await fetchBrands();
        console.log('🔍 Raw API response:', data);

        // Ensure brands is always an array
        const brandsData = data.data || data;
        console.log('🔍 Processed brands data:', brandsData);

        // Log each brand's actif field to debug the status issue and normalize the data
        if (Array.isArray(brandsData)) {
          const normalizedBrands = brandsData.map((brand) => {
            // Check for different possible field names for active status
            const actifValue =
              brand.actif !== undefined
                ? brand.actif
                : brand.active !== undefined
                  ? brand.active
                  : brand.is_active !== undefined
                    ? brand.is_active
                    : brand.status !== undefined
                      ? brand.status
                      : true; // Default to true if no status field found

            const originalActif = actifValue;
            const normalizedActif = convertActifToBoolean(actifValue);

            console.log(`🔍 Brand ${brand.nom_marque}:`, {
              id: brand.id,
              originalActif: originalActif,
              originalType: typeof originalActif,
              normalizedActif: normalizedActif,
              normalizedType: typeof normalizedActif,
              allFields: Object.keys(brand),
              brandObject: brand
            });

            // Show alert for first brand to debug (remove this after debugging)
            if (brand.id === brandsData[0]?.id) {
              alert(
                `Debug Info for ${brand.nom_marque}:\nOriginal actif: ${JSON.stringify(originalActif)} (${typeof originalActif})\nNormalized: ${normalizedActif}\nAll fields: ${Object.keys(brand).join(', ')}`
              );
            }

            return {
              ...brand,
              actif: normalizedActif
            };
          });

          setBrands(normalizedBrands);
        } else {
          setBrands([]);
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError(err.message || 'Erreur lors du chargement des marques');
      } finally {
        setLoading(false);
      }
    };

    loadBrands();
  }, []);

  // Gestion des changements du formulaire
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Ouvrir le modal pour créer une nouvelle marque
  const handleCreate = () => {
    setModalAction('create');
    setCurrentBrand(null);
    setFormData({
      nom_marque: '',
      description_marque: '',
      logo_marque: '',
      site_web: '',
      actif: true
    });
    setShowModal(true);
  };

  // Ouvrir le modal pour modifier une marque
  const handleEdit = (brand) => {
    setModalAction('edit');
    setCurrentBrand(brand);
    const normalizedActif = convertActifToBoolean(brand.actif);

    console.log('🔍 Edit brand - actif conversion:', {
      original: brand.actif,
      originalType: typeof brand.actif,
      normalized: normalizedActif,
      normalizedType: typeof normalizedActif
    });

    setFormData({
      nom_marque: brand.nom_marque || '',
      description_marque: brand.description_marque || '',
      logo_marque: brand.logo_marque || '',
      site_web: brand.site_web || '',
      actif: normalizedActif
    });
    setShowModal(true);
  };

  // Supprimer une marque
  const handleDelete = async (id) => {
    const confirmMessage = `Êtes-vous sûr de vouloir supprimer cette marque ?

⚠️ ATTENTION : Cette action est irréversible.

📝 IMPORTANT : Si cette marque contient des produits, la suppression échouera. Vous devez d'abord supprimer tous les produits de cette marque.

Voulez-vous continuer ?`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the API to delete the brand
      await deleteBrand(id);

      // Refresh the brands list from the server to ensure consistency
      const updatedData = await fetchBrands();
      const brandsData = updatedData.data || updatedData;
      setBrands(Array.isArray(brandsData) ? brandsData : []);

      setSuccess('Marque supprimée avec succès');
    } catch (err) {
      console.error('Erreur lors de la suppression:', err);
      setError(err.message || 'Erreur lors de la suppression de la marque');
    } finally {
      setLoading(false);
    }
  };

  // Soumettre le formulaire (création ou modification)
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      // Clean up the payload
      const payload = {
        nom_marque: formData.nom_marque.trim(),
        description_marque: formData.description_marque?.trim() || null,
        logo_marque: formData.logo_marque?.trim() || null,
        site_web: formData.site_web?.trim() || null,
        actif: Boolean(formData.actif)
      };

      if (modalAction === 'create') {
        await createBrand(payload);
      } else {
        await updateBrand(currentBrand.id, payload);
      }

      setSuccess(`Marque ${modalAction === 'create' ? 'créée' : 'modifiée'} avec succès!`);

      // Mettre à jour la liste des marques
      const updatedData = await fetchBrands();
      const brandsData = updatedData.data || updatedData;
      setBrands(Array.isArray(brandsData) ? brandsData : []);

      setShowModal(false);
    } catch (err) {
      console.error('Erreur:', err);
      setError(err.message || "Erreur lors de l'opération");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="py-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-4">
        <Breadcrumb.Item href="#/dashboard">
          <FaHome className="me-1" />
          Accueil
        </Breadcrumb.Item>
        <Breadcrumb.Item active>
          <FaTags className="me-1" />
          Gestion des Marques
        </Breadcrumb.Item>
      </Breadcrumb>

      <h2 className="mb-4">Gestion des Marques</h2>

      {/* Messages d'alerte */}
      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess(null)} dismissible>
          <i className="fas fa-check-circle me-2"></i>
          {success}
        </Alert>
      )}

      {/* Bouton pour ajouter une marque */}
      <div className="d-flex justify-content-end mb-4">
        <Button variant="primary" onClick={handleCreate}>
          <FaPlus className="me-2" />
          Ajouter une marque
        </Button>
      </div>

      {/* Tableau des marques */}
      {loading ? (
        <div className="text-center my-5">
          <Spinner animation="border" variant="primary" />
          <p>Chargement des marques...</p>
        </div>
      ) : brands.length === 0 ? (
        <Alert variant="info">Aucune marque disponible</Alert>
      ) : (
        <Card>
          <Card.Body>
            <div className="table-responsive">
              <Table hover responsive className="align-middle mb-0">
                <thead>
                  <tr className="bg-light">
                    <th className="ps-3" style={{ width: '60px' }}>
                      ID
                    </th>
                    <th style={{ width: '20%' }}>Logo</th>
                    <th style={{ width: '25%' }}>Nom</th>
                    <th style={{ width: '30%' }}>Description</th>
                    <th style={{ width: '15%' }}>Statut</th>
                    <th className="text-end pe-3" style={{ width: '15%' }}>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {brands.map((brand) => {
                    // Debug the actif field for each brand in the table
                    console.log(`🔍 Rendering brand ${brand.nom_marque}:`, {
                      actif: brand.actif,
                      actifType: typeof brand.actif,
                      actifBoolean: Boolean(brand.actif),
                      actifTruthy: !!brand.actif
                    });

                    return (
                      <tr key={brand.id} className="border-bottom">
                        <td className="ps-3 fw-medium">{brand.id}</td>
                        <td>
                          {brand.logo_marque ? (
                            <img
                              src={brand.logo_marque}
                              alt={brand.nom_marque}
                              style={{ width: '50px', height: '50px', objectFit: 'contain' }}
                              className="rounded"
                            />
                          ) : (
                            <div
                              className="bg-light rounded d-flex align-items-center justify-content-center"
                              style={{ width: '50px', height: '50px' }}
                            >
                              <FaImage className="text-muted" />
                            </div>
                          )}
                        </td>
                        <td>
                          <div className="fw-medium">{brand.nom_marque}</div>
                          {brand.site_web && (
                            <small className="text-muted">
                              <a href={brand.site_web} target="_blank" rel="noopener noreferrer">
                                {brand.site_web}
                              </a>
                            </small>
                          )}
                        </td>
                        <td>
                          <div className="text-truncate" style={{ maxWidth: '200px' }}>
                            {brand.description_marque || '-'}
                          </div>
                        </td>
                        <td>
                          <Badge bg={brand.actif ? 'success' : 'danger'}>{brand.actif ? 'Active' : 'Inactive'}</Badge>
                          <br />
                          <small className="text-muted">
                            Debug: {JSON.stringify(brand.actif)} ({typeof brand.actif})
                          </small>
                        </td>
                        <td className="text-end pe-3">
                          <Button
                            size="sm"
                            variant="outline-primary"
                            className="me-2"
                            onClick={() => handleEdit(brand)}
                            title="Modifier la marque"
                          >
                            <FaEdit />
                          </Button>
                          <Button size="sm" variant="outline-danger" onClick={() => handleDelete(brand.id)} title="Supprimer la marque">
                            <FaTrash />
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </Table>
            </div>
          </Card.Body>
        </Card>
      )}

      {/* Modal pour créer/modifier une marque */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{modalAction === 'create' ? 'Ajouter une marque' : 'Modifier la marque'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Nom de la marque <span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control type="text" name="nom_marque" value={formData.nom_marque} onChange={handleChange} required />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Site web</Form.Label>
                  <Form.Control
                    type="url"
                    name="site_web"
                    value={formData.site_web}
                    onChange={handleChange}
                    placeholder="https://example.com"
                  />
                </Form.Group>
              </Col>

              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    name="description_marque"
                    value={formData.description_marque}
                    onChange={handleChange}
                    rows={3}
                  />
                </Form.Group>
              </Col>

              <Col md={8}>
                <Form.Group className="mb-3">
                  <Form.Label>URL du logo</Form.Label>
                  <Form.Control
                    type="url"
                    name="logo_marque"
                    value={formData.logo_marque}
                    onChange={handleChange}
                    placeholder="https://example.com/logo.png"
                  />
                </Form.Group>
              </Col>

              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Check type="checkbox" name="actif" label="Marque active" checked={formData.actif} onChange={handleChange} />
                </Form.Group>
              </Col>
            </Row>
          </Form>

          {/* Image management for selected brand */}
          {modalAction === 'edit' && currentBrand && (
            <div className="mt-4 pt-3 border-top">
              <h6 className="mb-3 text-muted">Gestion des images</h6>
              <ImageManager modelType="marque" modelId={currentBrand.id} />
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={handleSubmit} disabled={loading}>
            {loading ? (
              <>
                <Spinner as="span" size="sm" animation="border" className="me-2" />
                Enregistrement...
              </>
            ) : (
              <>
                <FaCheck className="me-2" />
                {modalAction === 'create' ? 'Créer' : 'Modifier'}
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default BrandManagement;
