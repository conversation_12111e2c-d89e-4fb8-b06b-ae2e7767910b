import React, { useState, useEffect } from 'react';
import EditProduit from '../Forms/EditProduit';
import axios from 'axios';
import { <PERSON><PERSON>, <PERSON>b, <PERSON><PERSON>, Spinner, Card, Container, Row, Col, But<PERSON>, Pagination, Form, Modal, Badge } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import {
  fetchProducts,
  fetchProductById,
  updateProduct,
  deleteProduct,
  searchProducts,
  fetchProductsPaginated
} from '../../services/productService';

// Configuration Axios
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

const fontStyle = {
  fontFamily: "'Montserrat', sans-serif",
  fontWeight: 500
};

const colors = {
  primaryDark: '#2a3f5f',
  primaryLight: '#3a537b',
  accent: '#3a8dde',
  lightGray: '#f8f9fa'
};

const API_BASE_URL = import.meta.env.REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';
const PRODUCTS_PER_PAGE = 8;

const BrandCatalog = () => {
  // États
  const [brands, setBrands] = useState([]);
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [loading, setLoading] = useState({
    brands: false,
    products: false,
    productDetails: false,
    saving: false
  });
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('brands');
  const [selectedBrand, setSelectedBrand] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [showDeleteBrandModal, setShowDeleteBrandModal] = useState(false);
  const [brandToDelete, setBrandToDelete] = useState(null);
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [brandEditMode, setBrandEditMode] = useState(false);
  const [currentBrand, setCurrentBrand] = useState(null);
  const [brandForm, setBrandForm] = useState({
    nom_marque: '',
    description_marque: '',
    logo_marque: '',
    site_web: '',
    actif: true
  });

  // Show delete confirmation modal
  const handleShowDeleteModal = (product) => {
    // Safety check for null/undefined product
    if (!product || !product.id) {
      console.error('Invalid product data for deletion:', product);
      return;
    }
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  // Hide delete confirmation modal
  const handleCloseDeleteModal = () => {
    setProductToDelete(null);
    setShowDeleteModal(false);
  };

  // Delete product
  const handleDeleteProduct = async () => {
    if (!productToDelete?.id) return;
    setLoading((prev) => ({ ...prev, products: true }));
    try {
      await axios.delete(`${API_BASE_URL}/produits/${productToDelete.id}`);
      const updatedProducts = products.filter((p) => p.id !== productToDelete.id);
      setProducts(updatedProducts);
      setFilteredProducts(updatedProducts);
      handleCloseDeleteModal();
    } catch (err) {
      handleApiError(err, 'Erreur lors de la suppression du produit');
    } finally {
      setLoading((prev) => ({ ...prev, products: false }));
    }
  };

  // Show delete brand confirmation modal
  const handleShowDeleteBrandModal = (brand) => {
    setBrandToDelete(brand);
    setShowDeleteBrandModal(true);
  };

  // Hide delete brand confirmation modal
  const handleCloseDeleteBrandModal = () => {
    setBrandToDelete(null);
    setShowDeleteBrandModal(false);
  };

  // Delete brand
  const handleDeleteBrand = async () => {
    if (!brandToDelete?.id) return;
    setLoading((prev) => ({ ...prev, brands: true }));
    try {
      // Call the API to delete the brand
      await axios.delete(`${API_BASE_URL}/marques/${brandToDelete.id}`);

      // Refresh the brands list from the server to ensure consistency
      await fetchBrands();

      handleCloseDeleteBrandModal();
      // If the deleted brand was selected, go back to brands tab
      if (selectedBrand?.id === brandToDelete.id) {
        setSelectedBrand(null);
        setActiveTab('brands');
      }
    } catch (err) {
      handleApiError(err, 'Erreur lors de la suppression de la marque');
    } finally {
      setLoading((prev) => ({ ...prev, brands: false }));
    }
  };

  // Handle brand form changes
  const handleBrandFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setBrandForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Open brand modal for adding
  const handleAddBrand = () => {
    setBrandEditMode(false);
    setCurrentBrand(null);
    setBrandForm({
      nom_marque: '',
      description_marque: '',
      logo_marque: '',
      site_web: '',
      actif: true
    });
    setShowBrandModal(true);
  };

  // Open brand modal for editing
  const handleEditBrand = (brand) => {
    setBrandEditMode(true);
    setCurrentBrand(brand);
    setBrandForm({
      nom_marque: brand.nom_marque || '',
      description_marque: brand.description_marque || '',
      logo_marque: brand.logo_marque || '',
      site_web: brand.site_web || '',
      actif: brand.actif !== undefined ? brand.actif : true
    });
    setShowBrandModal(true);
  };

  // Close brand modal
  const handleCloseBrandModal = () => {
    setShowBrandModal(false);
    setBrandEditMode(false);
    setCurrentBrand(null);
    setBrandForm({
      nom_marque: '',
      description_marque: '',
      logo_marque: '',
      site_web: '',
      actif: true
    });
  };

  // Save brand (create or update)
  const handleSaveBrand = async () => {
    try {
      setLoading((prev) => ({ ...prev, brands: true }));
      setError(null);

      const payload = {
        nom_marque: brandForm.nom_marque.trim(),
        description_marque: brandForm.description_marque?.trim() || null,
        logo_marque: brandForm.logo_marque?.trim() || null,
        site_web: brandForm.site_web?.trim() || null,
        actif: Boolean(brandForm.actif)
      };

      if (brandEditMode && currentBrand) {
        // Update existing brand
        await axios.put(`${API_BASE_URL}/marques/${currentBrand.id}`, payload);
        const updatedBrands = brands.map((b) => (b.id === currentBrand.id ? { ...b, ...payload } : b));
        setBrands(updatedBrands);
      } else {
        // Create new brand
        const response = await axios.post(`${API_BASE_URL}/marques`, payload);
        setBrands([...brands, response.data]);
      }

      handleCloseBrandModal();
    } catch (err) {
      handleApiError(err, `Erreur lors de ${brandEditMode ? 'la modification' : 'la création'} de la marque`);
    } finally {
      setLoading((prev) => ({ ...prev, brands: false }));
    }
  };
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showEditProduit, setShowEditProduit] = useState(false);
  const [editProduitData, setEditProduitData] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [showProductModal, setShowProductModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState(null);

  // Form state
  const [productForm, setProductForm] = useState({
    nom_produit: '',
    reference_produit: '',
    prix_produit: '',
    quantite_produit: 0,
    image_produit: '',
    description_produit: ''
  });

  // Pagination
  const indexOfLastProduct = currentPage * PRODUCTS_PER_PAGE;
  const indexOfFirstProduct = indexOfLastProduct - PRODUCTS_PER_PAGE;
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  const totalPages = Math.ceil(filteredProducts.length / PRODUCTS_PER_PAGE);

  // Effects
  useEffect(() => {
    // Load Google Font
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    fetchBrands();
  }, []);

  useEffect(() => {
    if (products.length > 0) {
      handleSearch();
    }
  }, [searchTerm, products, selectedCategory, selectedSubCategory]);

  // API Functions
  const fetchBrands = async () => {
    try {
      setLoading((prev) => ({ ...prev, brands: true }));
      setError(null);
      const response = await axios.get(`${API_BASE_URL}/marques`);
      setBrands(response.data || []);
    } catch (err) {
      handleApiError(err, 'Erreur de chargement des marques');
    } finally {
      setLoading((prev) => ({ ...prev, brands: false }));
    }
  };

  const fetchProducts = async (brand) => {
    if (!brand?.id) {
      console.error('Invalid brand data for fetching products:', brand);
      return;
    }

    try {
      setSelectedBrand(brand);
      setCurrentPage(1);
      setSearchTerm('');
      setSelectedCategory(null);
      setSelectedSubCategory(null);
      setLoading((prev) => ({ ...prev, products: true }));
      setError(null);

      // Fetch products (without images)
      const response = await axios.get(`${API_BASE_URL}/marques/${brand.id}/produits`, {
        params: {
          with: 'sousSousCategorie,sousSousCategorie.sousCategorie,sousSousCategorie.sousCategorie.categorie'
        }
      });
      const productsData = response.data || [];

      // Fetch images for each product
      const productsWithImages = await Promise.all(
        productsData.map(async (product) => {
          try {
            const imgRes = await axios.get(`${API_BASE_URL}/images/get`, {
              params: { model_type: 'produit', model_id: product.id }
            });
            return { ...product, images: imgRes.data.images || [] };
          } catch {
            return { ...product, images: [] };
          }
        })
      );

      setProducts(productsWithImages);
      setFilteredProducts(productsWithImages);
      setActiveTab('products');
    } catch (err) {
      handleApiError(err, 'Erreur de chargement des produits');
    } finally {
      setLoading((prev) => ({ ...prev, products: false }));
    }
  };

  const fetchProductDetails = async (productId) => {
    if (!productId) return;

    try {
      setLoading((prev) => ({ ...prev, productDetails: true }));
      setError(null);

      // Fetch product details (without images)
      const response = await axios.get(`${API_BASE_URL}/produits/${productId}`, {
        params: {
          with: 'sousSousCategorie,sousSousCategorie.sousCategorie,sousSousCategorie.sousCategorie.categorie'
        }
      });
      const productData = response.data || {};

      // Fetch images for this product
      let images = [];
      try {
        const imgRes = await axios.get(`${API_BASE_URL}/images/get`, {
          params: { model_type: 'produit', model_id: productId }
        });
        images = imgRes.data.images || [];
      } catch {}

      const productWithImages = { ...productData, images };
      setSelectedProduct(productWithImages);
      setProductForm({
        nom_produit: productWithImages.nom_produit || '',
        reference_produit: productWithImages.reference_produit || '',
        prix_produit: productWithImages.prix_produit || '',
        quantite_produit: productWithImages.quantite_produit || 0,
        description_produit: productWithImages.description_produit || ''
      });
      setShowProductModal(true);
    } catch (err) {
      handleApiError(err, 'Erreur de chargement des détails');
    } finally {
      setLoading((prev) => ({ ...prev, productDetails: false }));
    }
  };

  const handleApiError = (error, defaultMessage) => {
    console.error(error);
    setError(error.response?.data?.message || error.message || defaultMessage);
  };

  // Filter and Search
  const handleSearch = () => {
    const term = searchTerm.toLowerCase().trim();

    let results = [...products];

    // Filter by search term
    if (term) {
      results = results.filter(
        (product) => product.nom_produit?.toLowerCase().includes(term) || product.reference_produit?.toLowerCase().includes(term)
      );
    }

    // Filter by category
    if (selectedCategory) {
      results = results.filter((product) => product.sousSousCategorie?.sousCategorie?.categorie_id === selectedCategory.id);
    }

    // Filter by subcategory
    if (selectedSubCategory) {
      results = results.filter((product) => product.sousSousCategorie?.sous_categorie_id === selectedSubCategory.id);
    }

    setFilteredProducts(results);
    setCurrentPage(1);
  };

  // Product Handlers
  const handleProductClick = (product) => {
    if (product?.id) {
      fetchProductDetails(product.id);
    }
  };

  const handleEditClick = () => {
    setEditMode(true);
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setProductForm((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveChanges = async () => {
    if (!selectedProduct?.id) return;

    try {
      setLoading((prev) => ({ ...prev, saving: true }));
      setError(null);

      console.log('🔄 Updating product with ID:', selectedProduct.id);
      console.log('📝 Product form data:', productForm);

      const response = await axios.put(`${API_BASE_URL}/produits/${selectedProduct.id}`, productForm);

      console.log('✅ Product updated successfully:', response.data);

      // Refresh the products list from server to ensure consistency
      if (selectedBrand) {
        await fetchProducts(selectedBrand);
      }

      setSelectedProduct((prev) => ({ ...prev, ...productForm }));
      setEditMode(false);
    } catch (err) {
      console.error('❌ Error updating product:', err);
      handleApiError(err, 'Erreur lors de la mise à jour du produit');
    } finally {
      setLoading((prev) => ({ ...prev, saving: false }));
    }
  };

  // UI Components
  const renderPagination = () =>
    totalPages > 1 && (
      <div className="d-flex justify-content-center my-4">
        <Pagination className="mb-0">
          <Pagination.Prev onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))} disabled={currentPage === 1} />
          {Array.from({ length: totalPages }, (_, i) => (
            <Pagination.Item
              key={i + 1}
              active={i + 1 === currentPage}
              onClick={() => setCurrentPage(i + 1)}
              style={{
                margin: '0 4px',
                minWidth: '40px',
                textAlign: 'center',
                fontWeight: i + 1 === currentPage ? 600 : 500,
                border: i + 1 === currentPage ? 'none' : '1px solid #dee2e6'
              }}
            >
              {i + 1}
            </Pagination.Item>
          ))}
          <Pagination.Next onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))} disabled={currentPage === totalPages} />
        </Pagination>
      </div>
    );

  const handleEditProduit = (product) => {
    // Safety check for null/undefined product
    if (!product || !product.id) {
      console.error('Invalid product data for editing:', product);
      return;
    }
    setEditProduitData(product);
    setShowEditProduit(true);
  };

  const handleEditProduitSuccess = () => {
    setShowEditProduit(false);
    setEditProduitData(null);
    // Refresh products for the selected brand
    if (selectedBrand) fetchProducts(selectedBrand);
  };

  // Utility function to get the primary image URL
  const getPrimaryImageUrl = (product) => {
    // Add null/undefined checks
    if (!product) {
      return 'https://via.placeholder.com/300';
    }

    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      const primary = product.images.find((img) => img.is_primary) || product.images[0];
      // Prefer direct_url, then url, then thumbnail_large, then thumbnail_medium, then path
      return (
        primary.direct_url ||
        primary.url ||
        primary.thumbnail_large ||
        primary.thumbnail_medium ||
        (primary.path ? `${API_BASE_URL}/images/file/${primary.path}` : null) ||
        'https://via.placeholder.com/300'
      );
    }
    return 'https://via.placeholder.com/300';
  };

  const renderProductCard = (product) => {
    // Safety check for null/undefined product
    if (!product) {
      return null;
    }

    return (
      <Card className="h-100 border-0 shadow-sm product-card position-relative">
        <Button
          variant="danger"
          size="sm"
          style={{
            position: 'absolute',
            top: 10,
            right: 10,
            zIndex: 2,
            display: 'flex',
            alignItems: 'center',
            gap: '0.25rem',
            padding: '0.25rem 0.5rem'
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleShowDeleteModal(product);
          }}
          title="Supprimer le produit"
        >
          <i className="fas fa-trash"></i>
          <span style={{ fontSize: '0.85em', fontWeight: 500 }}>Supprimer</span>
        </Button>
        <div className="product-image-container" onClick={() => handleProductClick(product)}>
          <Card.Img variant="top" src={getPrimaryImageUrl(product)} alt={product?.nom_produit || 'Product'} className="product-image" />
        </div>
        <Card.Body className="d-flex flex-column">
          <Button
            variant="outline-primary"
            size="sm"
            className="mb-2 align-self-end"
            onClick={(e) => {
              e.stopPropagation();
              handleEditProduit(product);
            }}
          >
            Éditer
          </Button>
          <Card.Title className="product-title">{product.nom_produit}</Card.Title>
          <Card.Text className="product-reference">Réf: {product.reference_produit || 'N/A'}</Card.Text>

          {product.sousSousCategorie?.sousCategorie?.categorie && (
            <div className="product-categories mb-2">
              <Badge bg="light" text="dark" className="me-1">
                {product.sousSousCategorie.sousCategorie.categorie.nom}
              </Badge>
              <Badge bg="light" text="dark">
                {product.sousSousCategorie.sousCategorie.nom}
              </Badge>
            </div>
          )}

          <div className="product-footer mt-auto">
            <span className="product-price">{product.prix_produit ? `${product.prix_produit} €` : 'Prix sur demande'}</span>
            <span className={`product-stock ${product.quantite_produit > 0 ? 'in-stock' : 'out-of-stock'}`}>
              {product.quantite_produit > 0 ? 'En stock' : 'Rupture'}
            </span>
          </div>
        </Card.Body>
      </Card>
    );
  };

  return (
    <Container className="py-4 catalog-container" style={fontStyle}>
      {/* Header */}
      <div className="text-center mb-5 catalog-header">
        <h1 className="catalog-title">CATALOGUE DES MARQUES</h1>
        <div className="catalog-divider" />
      </div>

      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          {error}
        </Alert>
      )}

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-4 catalog-tabs">
        {/* Brands Tab */}
        <Tab eventKey="brands" title="Marques">
          {/* Add Brand Button */}
          <div className="d-flex justify-content-end mb-4">
            <Button variant="primary" onClick={handleAddBrand}>
              <i className="fas fa-plus me-2"></i>
              Ajouter une marque
            </Button>
          </div>

          {loading.brands ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
            </div>
          ) : brands.length > 0 ? (
            <Row xs={1} md={2} lg={3} xl={4} className="g-4">
              {brands.map((brand) => (
                <Col key={brand.id}>
                  <Card className="h-100 brand-card">
                    <div onClick={() => fetchProducts(brand)} style={{ cursor: 'pointer' }}>
                      <Card.Body className="brand-card-body">
                        <div className="brand-logo-container">
                          <img src={brand.logo_marque} alt={brand.nom_marque} className="brand-logo" />
                        </div>
                        <Card.Title className="brand-name">{brand.nom_marque}</Card.Title>
                      </Card.Body>
                    </div>
                    {/* Action buttons at the bottom */}
                    <Card.Footer className="bg-transparent border-0 pt-0">
                      <div className="d-flex justify-content-center gap-2">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditBrand(brand);
                          }}
                          title="Modifier la marque"
                          className="flex-fill"
                        >
                          <i className="fas fa-edit me-1"></i>
                          Modifier
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShowDeleteBrandModal(brand);
                          }}
                          title="Supprimer la marque"
                          className="flex-fill"
                        >
                          <i className="fas fa-trash me-1"></i>
                          Supprimer
                        </Button>
                      </div>
                    </Card.Footer>
                  </Card>
                </Col>
              ))}
            </Row>
          ) : (
            <Alert variant="info" className="text-center">
              Aucune marque disponible
            </Alert>
          )}
        </Tab>

        {/* Products Tab */}
        <Tab eventKey="products" title="Produits" disabled={!selectedBrand}>
          {selectedBrand && (
            <>
              <div className="brand-header mb-4">
                <div className="brand-info">
                  <div className="brand-logo-title">
                    <div className="brand-logo-small">
                      <img src={selectedBrand.logo_marque} alt={selectedBrand.nom_marque} />
                    </div>
                    <h2 className="brand-title">{selectedBrand.nom_marque}</h2>
                  </div>
                  {selectedBrand.description_marque && <p className="brand-description">{selectedBrand.description_marque}</p>}
                </div>
                <Button variant="outline-primary" onClick={() => setActiveTab('brands')} className="back-button">
                  ← Retour aux marques
                </Button>
              </div>

              {/* Filters */}
              <div className="filters mb-4">
                <Row>
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Catégorie</Form.Label>
                      <Form.Select
                        value={selectedCategory?.id || ''}
                        onChange={(e) => {
                          const catId = e.target.value;
                          setSelectedCategory(catId ? categories.find((c) => c.id === parseInt(catId)) : null);
                          setSelectedSubCategory(null);
                        }}
                        className="filter-select"
                      >
                        <option value="">Toutes les catégories</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.nom}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Sous-catégorie</Form.Label>
                      <Form.Select
                        value={selectedSubCategory?.id || ''}
                        onChange={(e) => {
                          const subCatId = e.target.value;
                          setSelectedSubCategory(subCatId ? subCategories.find((sc) => sc.id === parseInt(subCatId)) : null);
                        }}
                        disabled={!selectedCategory}
                        className="filter-select"
                      >
                        <option value="">Toutes les sous-catégories</option>
                        {selectedCategory &&
                          subCategories
                            .filter((sc) => sc.categorie_id === selectedCategory.id)
                            .map((subCategory) => (
                              <option key={subCategory.id} value={subCategory.id}>
                                {subCategory.nom}
                              </option>
                            ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Recherche</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Rechercher par nom ou référence..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <div className="results-count">{filteredProducts.length} produit(s) trouvé(s)</div>
              </div>

              {renderPagination()}

              {loading.products ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                </div>
              ) : filteredProducts.length > 0 ? (
                <>
                  <Row xs={1} md={2} lg={3} xl={4} className="g-4 mb-4">
                    {currentProducts
                      .filter((product) => product && product.id)
                      .map((product) => (
                        <Col key={product.id}>{renderProductCard(product)}</Col>
                      ))}
                  </Row>
                  {renderPagination()}
                </>
              ) : (
                <Alert variant="info" className="no-results">
                  {products.length === 0
                    ? 'Aucun produit trouvé pour cette marque'
                    : 'Aucun produit trouvé correspondant à votre recherche'}
                </Alert>
              )}
            </>
          )}
        </Tab>
      </Tabs>

      {/* Product Modal */}
      {/* Edit Produit Modal */}
      {showEditProduit && editProduitData && (
        <EditProduit
          product={editProduitData}
          onClose={() => {
            setShowEditProduit(false);
            setEditProduitData(null);
          }}
          onSuccess={handleEditProduitSuccess}
        />
      )}

      <Modal
        show={showProductModal}
        onHide={() => setShowProductModal(false)}
        size="lg"
        centered
        className="product-modal professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-info text-white">
          <Modal.Title>Détails du Produit</Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          {loading.productDetails ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
            </div>
          ) : (
            <Row>
              <Col md={6}>
                <div className="modal-product-image">
                  <img src={getPrimaryImageUrl(selectedProduct)} alt={selectedProduct?.nom_produit || 'Product'} />
                </div>
              </Col>
              <Col md={6}>
                <h3 className="modal-product-title">{selectedProduct?.nom_produit}</h3>
                <p className="modal-product-reference">Réf: {selectedProduct?.reference_produit || 'N/A'}</p>

                {selectedProduct?.sousSousCategorie?.sousCategorie?.categorie && (
                  <div className="modal-product-categories mb-3">
                    <h5>Catégories</h5>
                    <div>
                      <Badge bg="light" text="dark" className="me-2">
                        {selectedProduct.sousSousCategorie.sousCategorie.categorie.nom}
                      </Badge>
                      <Badge bg="light" text="dark" className="me-2">
                        {selectedProduct.sousSousCategorie.sousCategorie.nom}
                      </Badge>
                      <Badge bg="light" text="dark">
                        {selectedProduct.sousSousCategorie.nom}
                      </Badge>
                    </div>
                  </div>
                )}

                <div className="modal-product-price mb-3">
                  <h5>{selectedProduct?.prix_produit ? `${selectedProduct.prix_produit} €` : 'Prix sur demande'}</h5>
                  <span className={`stock-badge ${selectedProduct?.quantite_produit > 0 ? 'in-stock' : 'out-of-stock'}`}>
                    {selectedProduct?.quantite_produit > 0 ? 'En stock' : 'Rupture'}
                  </span>
                </div>

                {selectedProduct?.description_produit && (
                  <div className="modal-product-description">
                    <h5>Description</h5>
                    <p>{selectedProduct.description_produit}</p>
                  </div>
                )}
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowProductModal(false)}>
            Fermer
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              setShowProductModal(false);
              handleEditProduit(selectedProduct);
            }}
          >
            <i className="fas fa-edit me-1"></i>
            Modifier
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Product Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={handleCloseDeleteModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirmer la suppression</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>
            Êtes-vous sûr de vouloir supprimer le produit <strong>{productToDelete?.nom_produit}</strong> ?
          </p>
          <p className="text-muted">Cette action est irréversible.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseDeleteModal}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDeleteProduct} disabled={loading.products}>
            {loading.products ? (
              <>
                <Spinner as="span" size="sm" animation="border" className="me-2" />
                Suppression...
              </>
            ) : (
              'Supprimer'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Brand Confirmation Modal */}
      <Modal show={showDeleteBrandModal} onHide={handleCloseDeleteBrandModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirmer la suppression</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="alert alert-warning">
            <h6>
              <i className="fas fa-exclamation-triangle me-2"></i>Attention
            </h6>
            <p className="mb-2">
              Vous êtes sur le point de supprimer la marque <strong>{brandToDelete?.nom_marque}</strong>.
            </p>
            <p className="mb-2">
              <strong>⚠️ IMPORTANT :</strong> Si cette marque contient des produits, la suppression échouera.
            </p>
            <p className="mb-0">
              <strong>📝 Conseil :</strong> Supprimez d'abord tous les produits de cette marque avant de la supprimer.
            </p>
          </div>
          <p className="text-muted">Cette action est irréversible.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseDeleteBrandModal}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDeleteBrand} disabled={loading.brands}>
            {loading.brands ? (
              <>
                <Spinner as="span" size="sm" animation="border" className="me-2" />
                Suppression...
              </>
            ) : (
              'Supprimer'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Brand Form Modal (Add/Edit) */}
      <Modal show={showBrandModal} onHide={handleCloseBrandModal} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>{brandEditMode ? 'Modifier la marque' : 'Ajouter une marque'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Nom de la marque <span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control
                    type="text"
                    name="nom_marque"
                    value={brandForm.nom_marque}
                    onChange={handleBrandFormChange}
                    required
                    disabled={loading.brands}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Site web</Form.Label>
                  <Form.Control
                    type="url"
                    name="site_web"
                    value={brandForm.site_web}
                    onChange={handleBrandFormChange}
                    placeholder="https://example.com"
                    disabled={loading.brands}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description_marque"
                value={brandForm.description_marque}
                onChange={handleBrandFormChange}
                disabled={loading.brands}
              />
            </Form.Group>

            <Row>
              <Col md={8}>
                <Form.Group className="mb-3">
                  <Form.Label>URL du logo</Form.Label>
                  <Form.Control
                    type="url"
                    name="logo_marque"
                    value={brandForm.logo_marque}
                    onChange={handleBrandFormChange}
                    placeholder="https://example.com/logo.png"
                    disabled={loading.brands}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="actif"
                    label="Marque active"
                    checked={brandForm.actif}
                    onChange={handleBrandFormChange}
                    disabled={loading.brands}
                  />
                </Form.Group>
              </Col>
            </Row>

            {/* Logo preview */}
            {brandForm.logo_marque && (
              <div className="mb-3">
                <Form.Label>Aperçu du logo</Form.Label>
                <div
                  style={{
                    width: '100px',
                    height: '100px',
                    border: '1px solid #dee2e6',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f8f9fa'
                  }}
                >
                  <img
                    src={brandForm.logo_marque}
                    alt="Aperçu du logo"
                    style={{
                      maxWidth: '80px',
                      maxHeight: '80px',
                      objectFit: 'contain'
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                </div>
              </div>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseBrandModal} disabled={loading.brands}>
            Annuler
          </Button>
          <Button variant="primary" onClick={handleSaveBrand} disabled={loading.brands || !brandForm.nom_marque.trim()}>
            {loading.brands ? (
              <>
                <Spinner as="span" size="sm" animation="border" className="me-2" />
                {brandEditMode ? 'Modification...' : 'Création...'}
              </>
            ) : (
              <>
                <i className={`fas ${brandEditMode ? 'fa-save' : 'fa-plus'} me-2`}></i>
                {brandEditMode ? 'Modifier' : 'Créer'}
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      <style>{`
        .catalog-container {
          max-width: 1200px;
        }
        .catalog-header {
          position: relative;
        }
        .catalog-title {
          font-size: 2.2rem;
          font-weight: 600;
          color: ${colors.primaryDark};
          letter-spacing: 1px;
          margin-bottom: 1rem;
        }
        .catalog-divider {
          height: 3px;
          width: 120px;
          background: linear-gradient(90deg, rgba(58,83,155,0.2) 0%, rgba(58,83,155,1) 50%, rgba(58,83,155,0.2) 100%);
          border-radius: 3px;
          margin: 0 auto;
        }
        .brand-card {
          border-radius: 12px;
          transition: transform 0.2s, box-shadow 0.2s;
          border: none;
          overflow: hidden;
        }
        .brand-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .brand-card-body {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 1.5rem 1.5rem 1rem 1.5rem;
        }
        .brand-card .card-footer {
          padding: 0.75rem 1rem 1rem 1rem;
        }
        .brand-card .btn {
          font-size: 0.875rem;
          font-weight: 500;
          border-radius: 6px;
          transition: all 0.2s ease;
        }
        .brand-card .btn:hover {
          transform: translateY(-1px);
        }
        .brand-logo-container {
          width: 100px;
          height: 100px;
          background-color: ${colors.lightGray};
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1rem;
        }
        .brand-logo {
          max-height: 80%;
          max-width: 80%;
          object-fit: contain;
        }
        .brand-name {
          color: ${colors.primaryDark};
          font-weight: 600;
          text-align: center;
        }
        .brand-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 2rem;
        }
        .brand-info {
          flex: 1;
        }
        .brand-logo-title {
          display: flex;
          align-items: center;
          margin-bottom: 0.5rem;
        }
        .brand-logo-small {
          width: 60px;
          height: 60px;
          background-color: ${colors.lightGray};
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
        }
        .brand-logo-small img {
          max-height: 80%;
          max-width: 80%;
          object-fit: contain;
        }
        .brand-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: ${colors.primaryDark};
          margin: 0;
        }
        .brand-description {
          color: #6c757d;
          max-width: 800px;
          line-height: 1.5;
          margin: 0;
        }
        .back-button {
          border-width: 2px;
          font-weight: 500;
          margin-left: 1rem;
          white-space: nowrap;
        }
        .filters {
          background-color: white;
          padding: 1.5rem;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.05);
          margin-bottom: 2rem;
        }
        .filter-select, .search-input {
          border-radius: 20px;
          padding: 10px 20px;
          width: 100%;
        }
        .results-count {
          color: #6c757d;
          font-size: 0.875rem;
          margin-top: 0.5rem;
        }
        .product-card {
          border-radius: 12px;
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
          height: 100%;
          border: none;
        }
        .product-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .product-image-container {
          height: 200px;
          padding: 1rem;
          background-color: ${colors.lightGray};
          display: flex;
          align-items: center;
          border-radius: 12px 12px 0 0;
        }
        .product-image {
          max-height: 100%;
          max-width: 100%;
          object-fit: contain;
          margin: 0 auto;
        }
        .product-title {
          font-size: 1rem;
          font-weight: 600;
          color: ${colors.primaryLight};
          margin-bottom: 0.5rem;
        }
        .product-reference {
          color: #6c757d;
          font-size: 0.85rem;
          margin-bottom: 0.75rem;
        }
        .product-categories {
          margin-bottom: 0.75rem;
        }
        .product-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
        }
        .product-price {
          color: ${colors.accent};
          font-weight: bold;
          font-size: 1.1rem;
        }
        .product-stock {
        }
        .modal-product-categories {
          margin-bottom: 1rem;
        }
        .modal-product-price {
          display: flex;
          align-items: center;
          margin-bottom: 1.5rem;
        }
        .modal-product-price h5 {
          color: ${colors.accent};
          margin: 0;
          margin-right: 1rem;
        }
        .stock-badge {
          font-size: 0.875rem;
          padding: 0.35em 0.65em;
          border-radius: 50rem;
        }
        .modal-product-description h5 {
          color: ${colors.primaryDark};
          margin-bottom: 0.5rem;
        }
        .modal-product-description p {
          color: #495057;
          line-height: 1.6;
        }
      `}</style>
    </Container>
  );
};

export default BrandCatalog;
